package com.zkdiman.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Excel导出相关配置
 * 兼容EasyExcel 3.3.4版本
 */
@Configuration
@Slf4j
public class ExcelExportConfig {

    @PostConstruct
    public void configureNetworkSettings() {
        // 设置全局的网络超时参数，影响URL连接
        System.setProperty("sun.net.client.defaultConnectTimeout", "5000"); // 5秒连接超时
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");   // 10秒读取超时

        log.info("Excel导出配置已初始化：");
        log.info("- 网络连接超时: 5秒");
        log.info("- 网络读取超时: 10秒");
        log.info("- EasyExcel版本: 3.3.4");
    }
}
