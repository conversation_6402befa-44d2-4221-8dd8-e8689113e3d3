package com.zkdiman.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Excel导出相关配置
 * 兼容EasyExcel 4.0.3版本
 */
@Configuration
@Slf4j
public class ExcelExportConfig {

    @PostConstruct
    public void configureNetworkSettings() {
        // 设置全局的网络超时参数，影响URL连接
        System.setProperty("sun.net.client.defaultConnectTimeout", "5000"); // 5秒连接超时
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");   // 10秒读取超时

        // EasyExcel 4.0.3 特定配置
        System.setProperty("easyexcel.read.cache.selector", "simple_cache"); // 使用简单缓存策略

        log.info("Excel导出配置已初始化：");
        log.info("- 网络连接超时: 5秒");
        log.info("- 网络读取超时: 10秒");
        log.info("- EasyExcel版本: 4.0.3");
        log.info("- 缓存策略: simple_cache");
    }
}
