package com.zkdiman.server.handler;


import com.alibaba.excel.exception.ExcelGenerateException;
import com.alibaba.excel.exception.ExcelWriteDataConvertException;
import com.zkdiman.common.exception.BaseException;
import com.zkdiman.common.exception.BusinessException;
import com.zkdiman.common.exception.TimeException;
import com.zkdiman.common.result.Result;
import com.zkdiman.common.utils.ExcelUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 全局异常处理器，处理项目中抛出的业务异常
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LogManager.getLogger(GlobalExceptionHandler.class);

    /**
     * 捕获业务异常
     * @param ex
     * @return
     */
    @ExceptionHandler(BusinessException.class)
    public Result businessExceptionHandler(BusinessException ex){
        logger.error("业务异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获时间异常
     * @param ex
     * @return
     */
    @ExceptionHandler(TimeException.class)
    public Result timeExceptionHandler(TimeException ex){
        logger.warn("时间校验异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获基础异常（兜底处理）
     * @param ex
     * @return
     */
    @ExceptionHandler(BaseException.class)
    public Result baseExceptionHandler(BaseException ex){
        logger.error("基础异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获Excel数据转换异常
     * @param ex
     * @return
     */
    @ExceptionHandler(ExcelWriteDataConvertException.class)
    public void excelConvertExceptionHandler(ExcelWriteDataConvertException ex) {
        logger.error("Excel数据转换异常：{}", ex.getMessage(), ex);

        // 检查是否是Excel导出请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletResponse response = attributes.getResponse();
            if (response != null && !response.isCommitted()) {
                try {
                    response.reset();
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("text/plain;charset=UTF-8");

                    // 提供更友好的错误信息
                    String errorMessage = "Excel导出失败：数据转换异常";
                    if (ex.getMessage().contains("Convert data:") && ex.getMessage().contains("error")) {
                        errorMessage += "，可能是图片下载超时或网络问题导致";
                    }

                    response.getWriter().write(errorMessage);
                    response.getWriter().flush();
                } catch (IOException ioException) {
                    logger.error("发送Excel转换异常错误响应失败: {}", ioException.getMessage(), ioException);
                }
            }
        }
        // 注意：这里不返回Result对象，避免类型转换异常
    }

    /**
     * 捕获Excel生成异常（通常由IO问题导致）
     * @param ex
     * @return
     */
    @ExceptionHandler(ExcelGenerateException.class)
    public void excelGenerateExceptionHandler(ExcelGenerateException ex) {
        logger.error("Excel生成异常：{}", ex.getMessage(), ex);

        // 检查是否是Excel导出请求（响应类型已设置为Excel）
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletResponse response = attributes.getResponse();
            if (response != null && !response.isCommitted()) {
                // 如果响应还未提交，尝试发送错误信息
                try {
                    response.reset();
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("text/plain;charset=UTF-8");
                    response.getWriter().write("Excel导出失败，可能是网络连接中断导致");
                    response.getWriter().flush();
                } catch (IOException ioException) {
                    logger.error("发送Excel生成异常错误响应失败: {}", ioException.getMessage(), ioException);
                }
            } else {
                logger.warn("Excel生成异常，但响应已提交，无法发送错误信息");
            }
        }
        // 注意：这里不返回Result对象，因为响应类型可能已经设置为Excel格式
    }

    /**
     * 捕获自定义Excel导出异常
     * @param ex
     * @return
     */
    @ExceptionHandler(ExcelUtil.ExcelExportException.class)
    public void excelExportExceptionHandler(ExcelUtil.ExcelExportException ex) {
        logger.error("Excel导出异常：{}", ex.getMessage(), ex);

        // 检查是否是Excel导出请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletResponse response = attributes.getResponse();
            if (response != null && !response.isCommitted()) {
                try {
                    response.reset();
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("text/plain;charset=UTF-8");
                    response.getWriter().write("Excel导出失败: " + ex.getMessage());
                    response.getWriter().flush();
                } catch (IOException ioException) {
                    logger.error("发送Excel导出异常错误响应失败: {}", ioException.getMessage(), ioException);
                }
            }
        }
        // 注意：这里不返回Result对象，避免类型转换异常
    }


    /**
     * 处理请求参数格式错误 @RequestParam上validate失败后抛出的异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String message = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));
        logger.error("参数验证失败: {}", message);
        return Result.error("参数验证失败: " + message);
    }

    /**
     * 处理请求参数格式错误 @RequestBody上validate失败后抛出的异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<String> errors = e.getBindingResult().getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        String message = String.join("; ", errors);
        logger.error("参数验证失败: {}", message);
        return Result.error("参数验证失败: " + message);
    }

    /**
     * 处理请求参数格式错误 @RequestBody上validate失败后抛出的异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleBindException(BindException e) {
        List<String> errors = e.getBindingResult().getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        String message = String.join("; ", errors);
        logger.error("参数绑定失败: {}", message);
        return Result.error("参数绑定失败: " + message);
    }

    /**
     * 处理缺少请求参数的异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        logger.error("缺少请求参数: {}", e.getMessage());
        return Result.error("缺少请求参数: " + e.getParameterName());
    }

    /**
     * 处理缺少请求头的异常
     */
    @ExceptionHandler(MissingRequestHeaderException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMissingRequestHeaderException(MissingRequestHeaderException e) {
        logger.error("缺少请求头: {}", e.getMessage());
        return Result.error("缺少请求头: " + e.getHeaderName());
    }

    /**
     * 处理缺少上传文件的异常
     */
    @ExceptionHandler(MissingServletRequestPartException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMissingServletRequestPartException(MissingServletRequestPartException e) {
        logger.error("缺少上传文件: {}", e.getMessage());
        return Result.error("缺少上传文件: " + e.getRequestPartName());
    }

    /**
     * 处理所有未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleException(Exception e) {
        logger.error("服务器内部错误", e);

        // 检查是否是Excel导出相关的异常
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletResponse response = attributes.getResponse();
            if (response != null) {
                String contentType = response.getContentType();
                // 如果响应类型已经设置为Excel格式，不能返回JSON
                if (contentType != null && contentType.contains("spreadsheetml")) {
                    logger.warn("Excel导出过程中发生异常，响应类型已设置为Excel格式，无法返回JSON错误信息");
                    if (!response.isCommitted()) {
                        try {
                            response.reset();
                            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                            response.setContentType("text/plain;charset=UTF-8");
                            response.getWriter().write("服务器内部错误: " + e.getMessage());
                            response.getWriter().flush();
                        } catch (IOException ioException) {
                            logger.error("发送错误响应失败: {}", ioException.getMessage(), ioException);
                        }
                    }
                    return null; // 不返回Result对象
                }
            }
        }

        return Result.error("服务器内部错误: " + e.getMessage());
    }

}