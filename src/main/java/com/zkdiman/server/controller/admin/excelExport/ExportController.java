package com.zkdiman.server.controller.admin.excelExport;

import com.zkdiman.common.utils.ExcelUtil;
import com.zkdiman.pojo.dto.ExamLogExportDTO;


import com.zkdiman.server.service.ExamExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api(tags = "数据导出接口")
@RestController
@RequestMapping("/api/export") // 建议给API加个前缀，如/api
@RequiredArgsConstructor
public class ExportController {

    private final ExamExportService examExportService;

    @ApiOperation("根据考场ID导出学生答题记录Excel")
    @GetMapping("/exam-logs/{examId}")
    public void exportExamLogsByExamId(
            @ApiParam(value = "考场ID", required = true, example = "686244fc3723201fe5dc26b0")
            @PathVariable String examId,
            HttpServletResponse response) {

        // 1. 调用Service层，传入examId，获取处理好的数据列表
        List<ExamLogExportDTO> dataList = examExportService.generateExportDataByExamId(examId);

        // 2. 构造动态文件名
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String fileName = String.format("考场-%s-答题记录-%s.xlsx", examId, dateStr);
        String sheetName = "答题记录详情";

        // 3. 调用通用Excel工具类执行导出
        ExcelUtil.export(response, fileName, sheetName, dataList, ExamLogExportDTO.class);
    }
}
