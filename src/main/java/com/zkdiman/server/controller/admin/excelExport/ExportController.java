package com.zkdiman.server.controller.admin.excelExport;

import com.zkdiman.common.utils.ExcelUtil;
import com.zkdiman.pojo.dto.ExamLogExportDTO;


import com.zkdiman.server.service.ExamExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api(tags = "数据导出接口")
@RestController
@RequestMapping("/api/export") // 建议给API加个前缀，如/api
@RequiredArgsConstructor
@Slf4j
public class ExportController {

    private final ExamExportService examExportService;

    @ApiOperation("根据考场ID导出学生答题记录Excel")
    @GetMapping("/exam-logs/{examId}")
    public Result<Void> exportExamLogsByExamId(
            @ApiParam(value = "考场ID", required = true, example = "686244fc3723201fe5dc26b0")
            @PathVariable String examId,
            HttpServletResponse response) {

        log.info("开始导出考场 {} 的学生答题记录Excel", examId);

        try {
            // 1. 调用Service层，传入examId，获取处理好的数据列表
            List<ExamLogExportDTO> dataList = examExportService.generateExportDataByExamId(examId);
            log.info("考场 {} 共查询到 {} 条答题记录", examId, dataList.size());

            // 2. 构造动态文件名
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String fileName = String.format("考场-%s-答题记录-%s.xlsx", examId, dateStr);
            String sheetName = "答题记录详情";

            // 3. 调用通用Excel工具类执行导出
            ExcelUtil.export(response, fileName, sheetName, dataList, ExamLogExportDTO.class);
            log.info("考场 {} 的Excel导出完成，文件名: {}", examId, fileName);

        } catch (ExcelUtil.ExcelExportException e) {
            // Excel导出异常已经在ExcelUtil中处理了响应，这里只记录日志
            log.error("考场 {} 的Excel导出失败: {}", examId, e.getMessage(), e);
            // 不需要再次处理响应，因为ExcelUtil已经处理过了

        } catch (Exception e) {
            // 处理其他业务异常（如数据查询失败等）
            log.error("考场 {} 的Excel导出过程中发生未预期异常: {}", examId, e.getMessage(), e);

            // 只有在响应未提交的情况下才能发送错误响应
            if (!response.isCommitted()) {
                try {
                    response.reset();
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("text/plain;charset=UTF-8");
                    response.getWriter().write("导出失败: " + e.getMessage());
                    response.getWriter().flush();
                } catch (IOException ioException) {
                    log.error("发送错误响应失败: {}", ioException.getMessage(), ioException);
                }
            }
        }
        return Result.success("excel导出成功");
    }
}
