package com.zkdiman.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 图片下载工具类
 * 提供快速、安全的图片下载功能
 */
@Slf4j
public class ImageDownloadUtil {

    // 连接超时时间（毫秒）
    private static final int CONNECT_TIMEOUT = 3000; // 3秒
    
    // 读取超时时间（毫秒）
    private static final int READ_TIMEOUT = 5000; // 5秒
    
    // 最大文件大小（字节）- 2MB
    private static final int MAX_FILE_SIZE = 2 * 1024 * 1024;

    /**
     * 同步下载图片
     * 
     * @param urlString 图片URL
     * @return 图片字节数组，如果下载失败返回null
     */
    public static byte[] downloadImage(String urlString) {
        if (urlString == null || urlString.trim().isEmpty()) {
            return null;
        }

        try {
            URL url = new URL(urlString);
            URLConnection connection = url.openConnection();
            
            // 设置超时
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            
            // 设置User-Agent，避免某些服务器拒绝请求
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // 如果是HTTP连接，设置额外的属性
            if (connection instanceof HttpURLConnection) {
                HttpURLConnection httpConnection = (HttpURLConnection) connection;
                httpConnection.setRequestMethod("GET");
                httpConnection.setInstanceFollowRedirects(true);
                
                int responseCode = httpConnection.getResponseCode();
                if (responseCode < 200 || responseCode >= 400) {
                    log.debug("HTTP响应码异常: {}, URL: {}", responseCode, urlString);
                    return null;
                }
            }

            // 检查内容长度
            int contentLength = connection.getContentLength();
            if (contentLength > MAX_FILE_SIZE) {
                log.debug("图片文件过大: {} bytes, URL: {}", contentLength, urlString);
                return null;
            }

            try (InputStream inputStream = connection.getInputStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                int totalBytesRead = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    totalBytesRead += bytesRead;
                    
                    // 检查是否超过最大文件大小
                    if (totalBytesRead > MAX_FILE_SIZE) {
                        log.debug("图片文件过大，读取过程中超过限制: {} bytes, URL: {}", MAX_FILE_SIZE, urlString);
                        return null;
                    }
                    
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                byte[] imageData = outputStream.toByteArray();
                log.debug("图片下载成功，大小: {} bytes, URL: {}", imageData.length, urlString);
                return imageData;
            }
            
        } catch (SocketTimeoutException e) {
            log.debug("图片下载超时: {}, 错误: {}", urlString, e.getMessage());
            return null;
            
        } catch (IOException e) {
            log.debug("图片下载IO异常: {}, 错误: {}", urlString, e.getMessage());
            return null;
            
        } catch (Exception e) {
            log.debug("图片下载异常: {}, 错误: {}", urlString, e.getMessage());
            return null;
        }
    }

    /**
     * 异步下载图片
     * 
     * @param urlString 图片URL
     * @return CompletableFuture包装的图片字节数组
     */
    public static CompletableFuture<byte[]> downloadImageAsync(String urlString) {
        return CompletableFuture.supplyAsync(() -> downloadImage(urlString))
                .orTimeout(READ_TIMEOUT + CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                .exceptionally(throwable -> {
                    log.debug("异步图片下载失败: {}, 错误: {}", urlString, throwable.getMessage());
                    return null;
                });
    }
}
