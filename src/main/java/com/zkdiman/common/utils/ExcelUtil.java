package com.zkdiman.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelGenerateException;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 通用Excel导出工具类 (基于EasyExcel)
 * 封装了通用的导出逻辑，与具体业务解耦。
 */
@Slf4j
public class ExcelUtil {

    /**
     * 将数据列表导出为Excel文件并直接通过HTTP响应下载
     *
     * @param response  HttpServletResponse对象，用于设置响应头和获取输出流
     * @param fileName  导出的文件名 (例如: "学生答题记录.xlsx")
     * @param sheetName Excel工作表的名称 (例如: "答题记录")
     * @param dataList  要导出的数据列表，列表中的对象类型应与clazz对应
     * @param clazz     数据对象的Class类型，EasyExcel会根据此类的注解来生成表头和数据
     * @param <T>       泛型参数，代表任意的DTO类型
     * @throws ExcelExportException 当Excel导出失败时抛出此异常
     */
    public static <T> void export(HttpServletResponse response, String fileName, String sheetName, List<T> dataList, Class<T> clazz) throws ExcelExportException {
        try {
            // 检查响应是否已经被提交
            if (response.isCommitted()) {
                log.warn("响应已被提交，无法进行Excel导出: {}", fileName);
                throw new ExcelExportException("响应已被提交，无法进行Excel导出");
            }

            // 1. 设置响应头，告知浏览器这是一个需要下载的文件
            // Content-Type 指定了文件的MIME类型
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());

            // 2. 对文件名进行URL编码，以防止中文文件名乱码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            // 3. Content-Disposition 头指定了文件名和下载方式
            // attachment; 表示作为附件下载
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName);

            // 4. 使用EasyExcel将数据写入到response的输出流
            // .write(response.getOutputStream(), clazz) 指定了输出目标和数据模型
            // .sheet(sheetName) 指定了工作表名称
            // .registerWriteHandler(...) 注册一个写入处理器，这里用来自适应列宽，非常实用
            // .doWrite(dataList) 执行写入操作
            EasyExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(Boolean.TRUE) // 自动关闭流
                    .sheet(sheetName)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 注册一个自适应列宽的策略
                    .doWrite(dataList);

            log.info("Excel导出成功: {}", fileName);

        } catch (ExcelGenerateException e) {
            // 处理EasyExcel生成异常，通常是由于IO问题导致
            log.error("Excel生成异常: {}, 文件: {}", e.getMessage(), fileName, e);
            handleExportError(response, "Excel生成失败，可能是网络连接中断导致");
            throw new ExcelExportException("Excel生成失败: " + e.getMessage(), e);

        } catch (IOException e) {
            // 处理IO异常，如网络连接中断
            log.error("Excel导出IO异常: {}, 文件: {}", e.getMessage(), fileName, e);
            handleExportError(response, "文件传输失败，请检查网络连接");
            throw new ExcelExportException("Excel导出IO异常: " + e.getMessage(), e);

        } catch (Exception e) {
            // 处理其他未预期的异常
            log.error("Excel导出未知异常: {}, 文件: {}", e.getMessage(), fileName, e);
            handleExportError(response, "Excel导出失败");
            throw new ExcelExportException("Excel导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理导出错误，尝试向客户端发送错误信息
     *
     * @param response HTTP响应对象
     * @param errorMessage 错误信息
     */
    private static void handleExportError(HttpServletResponse response, String errorMessage) {
        try {
            // 只有在响应未提交的情况下才能设置状态和写入内容
            if (!response.isCommitted()) {
                response.reset(); // 重置响应
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write(errorMessage);
                response.getWriter().flush();
            } else {
                log.warn("响应已提交，无法发送错误信息: {}", errorMessage);
            }
        } catch (IOException ex) {
            log.error("发送错误响应失败: {}", ex.getMessage(), ex);
        }
    }

    /**
     * Excel导出异常类
     */
    public static class ExcelExportException extends Exception {
        public ExcelExportException(String message) {
            super(message);
        }

        public ExcelExportException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}