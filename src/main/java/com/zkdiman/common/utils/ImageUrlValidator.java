package com.zkdiman.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;

/**
 * 图片URL验证工具类
 * 用于在Excel导出前验证图片URL的可访问性
 */
@Slf4j
public class ImageUrlValidator {

    // 连接超时时间（毫秒）
    private static final int CONNECT_TIMEOUT = 3000; // 3秒
    
    // 读取超时时间（毫秒）
    private static final int READ_TIMEOUT = 5000; // 5秒
    
    // 最大文件大小（字节）- 5MB
    private static final int MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 验证URL是否可以正常访问
     * 
     * @param urlString 图片URL字符串
     * @return true如果URL可以正常访问，false如果有问题
     */
    public static boolean isUrlAccessible(String urlString) {
        if (urlString == null || urlString.trim().isEmpty()) {
            return false;
        }

        try {
            URL url = new URL(urlString);
            URLConnection connection = url.openConnection();
            
            // 设置超时
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            
            // 设置User-Agent，避免某些服务器拒绝请求
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // 如果是HTTP连接，使用HEAD请求检查
            if (connection instanceof HttpURLConnection) {
                HttpURLConnection httpConnection = (HttpURLConnection) connection;
                httpConnection.setRequestMethod("HEAD");
                httpConnection.setInstanceFollowRedirects(true);
                
                int responseCode = httpConnection.getResponseCode();
                int contentLength = httpConnection.getContentLength();
                
                // 检查响应码
                if (responseCode < 200 || responseCode >= 400) {
                    log.debug("URL响应码异常: {}, URL: {}", responseCode, urlString);
                    return false;
                }
                
                // 检查文件大小
                if (contentLength > MAX_FILE_SIZE) {
                    log.debug("图片文件过大: {} bytes, URL: {}", contentLength, urlString);
                    return false;
                }
                
                httpConnection.disconnect();
            }
            
            log.debug("URL验证成功: {}", urlString);
            return true;
            
        } catch (SocketTimeoutException e) {
            log.debug("URL连接超时: {}, 错误: {}", urlString, e.getMessage());
            return false;
            
        } catch (IOException e) {
            log.debug("URL连接IO异常: {}, 错误: {}", urlString, e.getMessage());
            return false;
            
        } catch (Exception e) {
            log.debug("URL验证异常: {}, 错误: {}", urlString, e.getMessage());
            return false;
        }
    }

    /**
     * 获取安全的URL，如果原URL有问题则返回null
     * 
     * @param urlString 原始URL字符串
     * @return 如果URL可访问则返回URL对象，否则返回null
     */
    public static URL getSafeUrl(String urlString) {
        if (!isUrlAccessible(urlString)) {
            return null;
        }
        
        try {
            return new URL(urlString);
        } catch (Exception e) {
            log.debug("URL格式异常: {}, 错误: {}", urlString, e.getMessage());
            return null;
        }
    }
}
