package com.zkdiman.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;

/**
 * 增强的URL图片转换器
 * 具有超时控制、重试机制和错误处理
 */
@Slf4j
public class RobustUrlImageConverter implements Converter<URL> {

    // 连接超时时间（毫秒）
    private static final int CONNECT_TIMEOUT = 5000; // 5秒
    
    // 读取超时时间（毫秒）
    private static final int READ_TIMEOUT = 10000; // 10秒
    
    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 2;
    
    // 最大文件大小（字节）- 5MB
    private static final int MAX_FILE_SIZE = 5 * 1024 * 1024;

    @Override
    public Class<URL> supportJavaTypeKey() {
        return URL.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.IMAGE;
    }

    @Override
    public WriteCellData<?> convertToExcelData(URL value, ExcelContentProperty contentProperty,
                                              GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            log.debug("URL为空，跳过图片转换");
            return new WriteCellData<>("");
        }

        String urlString = value.toString();
        log.debug("开始转换图片URL: {}", urlString);

        try {
            byte[] imageData = downloadImageWithRetry(urlString);

            if (imageData == null || imageData.length == 0) {
                log.warn("图片下载失败或为空，降级为URL文本: {}", urlString);
                // 返回包含URL文本的单元格，而不是图片
                WriteCellData<String> cellData = new WriteCellData<>();
                cellData.setStringValue(urlString);
                cellData.setType(CellDataTypeEnum.STRING);
                return cellData;
            }

            log.debug("图片下载成功，大小: {} bytes, URL: {}", imageData.length, urlString);
            WriteCellData<byte[]> cellData = new WriteCellData<>();
            cellData.setImageValue(imageData);
            cellData.setType(CellDataTypeEnum.IMAGE);
            return cellData;

        } catch (Exception e) {
            log.error("图片转换过程中发生异常，降级为URL文本: {}, 异常: {}", urlString, e.getMessage(), e);
            // 发生任何异常时，都降级为URL文本，确保Excel导出能够继续
            WriteCellData<String> cellData = new WriteCellData<>();
            cellData.setStringValue(urlString + " (图片加载失败)");
            cellData.setType(CellDataTypeEnum.STRING);
            return cellData;
        }
    }

    /**
     * 带重试机制的图片下载
     */
    private byte[] downloadImageWithRetry(String urlString) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            try {
                log.debug("尝试下载图片 (第{}次): {}", attempt, urlString);
                return downloadImage(urlString);
                
            } catch (SocketTimeoutException e) {
                lastException = e;
                log.warn("图片下载超时 (第{}次): {}, 错误: {}", attempt, urlString, e.getMessage());
                
                // 如果不是最后一次尝试，等待一下再重试
                if (attempt < MAX_RETRY_COUNT) {
                    try {
                        Thread.sleep(1000); // 等待1秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                
            } catch (IOException e) {
                lastException = e;
                log.warn("图片下载IO异常 (第{}次): {}, 错误: {}", attempt, urlString, e.getMessage());
                
                // 对于IO异常，不重试
                break;
                
            } catch (Exception e) {
                lastException = e;
                log.error("图片下载未知异常 (第{}次): {}, 错误: {}", attempt, urlString, e.getMessage(), e);
                break;
            }
        }
        
        log.error("图片下载最终失败，URL: {}, 最后异常: {}", urlString, 
                 lastException != null ? lastException.getMessage() : "未知");
        return null;
    }

    /**
     * 下载图片数据
     */
    private byte[] downloadImage(String urlString) throws IOException {
        URL url = new URL(urlString);
        URLConnection connection = url.openConnection();
        
        // 设置超时
        connection.setConnectTimeout(CONNECT_TIMEOUT);
        connection.setReadTimeout(READ_TIMEOUT);
        
        // 设置User-Agent，避免某些服务器拒绝请求
        connection.setRequestProperty("User-Agent", 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        // 如果是HTTP连接，设置额外的属性
        if (connection instanceof HttpURLConnection) {
            HttpURLConnection httpConnection = (HttpURLConnection) connection;
            httpConnection.setRequestMethod("GET");
            httpConnection.setInstanceFollowRedirects(true);
        }

        // 检查内容长度
        int contentLength = connection.getContentLength();
        if (contentLength > MAX_FILE_SIZE) {
            throw new IOException("图片文件过大: " + contentLength + " bytes, 最大允许: " + MAX_FILE_SIZE + " bytes");
        }

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            int totalBytesRead = 0;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                totalBytesRead += bytesRead;
                
                // 检查是否超过最大文件大小
                if (totalBytesRead > MAX_FILE_SIZE) {
                    throw new IOException("图片文件过大，读取过程中超过限制: " + MAX_FILE_SIZE + " bytes");
                }
                
                outputStream.write(buffer, 0, bytesRead);
            }
            
            return outputStream.toByteArray();
        }
    }
}
