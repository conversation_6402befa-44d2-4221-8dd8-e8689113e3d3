package com.zkdiman.common.converter;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.net.URL;

/**
 * RobustUrlImageConverter 测试类
 */
class RobustUrlImageConverterTest {

    private RobustUrlImageConverter converter;

    @BeforeEach
    void setUp() {
        converter = new RobustUrlImageConverter();
    }

    @Test
    void testSupportJavaTypeKey() {
        assertEquals(URL.class, converter.supportJavaTypeKey());
    }

    @Test
    void testSupportExcelTypeKey() {
        assertEquals(CellDataTypeEnum.IMAGE, converter.supportExcelTypeKey());
    }

    @Test
    void testConvertNullUrl() throws Exception {
        WriteCellData<?> result = converter.convertToExcelData(null, null, null);
        assertNotNull(result);
        assertEquals("", result.getStringValue());
    }

    @Test
    void testConvertValidImageUrl() throws Exception {
        // 使用一个公开的测试图片URL
        URL testUrl = new URL("https://httpbin.org/image/png");
        
        try {
            WriteCellData<?> result = converter.convertToExcelData(testUrl, null, null);
            assertNotNull(result);
            
            // 如果成功下载，应该是图片类型；如果失败，应该是字符串类型
            assertTrue(result.getType() == CellDataTypeEnum.IMAGE || 
                      result.getType() == CellDataTypeEnum.STRING);
                      
        } catch (Exception e) {
            // 网络问题导致的异常是可以接受的
            assertTrue(e.getMessage().contains("timeout") || 
                      e.getMessage().contains("connection"));
        }
    }

    @Test
    void testConvertInvalidUrl() throws Exception {
        URL invalidUrl = new URL("https://invalid-domain-that-does-not-exist.com/image.png");
        
        WriteCellData<?> result = converter.convertToExcelData(invalidUrl, null, null);
        assertNotNull(result);
        
        // 无效URL应该降级为字符串
        assertEquals(CellDataTypeEnum.STRING, result.getType());
        assertTrue(result.getStringValue().contains("invalid-domain-that-does-not-exist.com"));
    }
}
